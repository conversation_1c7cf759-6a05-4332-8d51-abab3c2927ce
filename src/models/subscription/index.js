import { SubscriptionPlan } from './SubscriptionPlan.js';
import { UserSubscription } from './UserSubscription.js';
import { PaymentTransaction } from './PaymentTransaction.js';
import { CreditReset } from './CreditReset.js';
import { User } from '../user/User.js';
import { UserCredit } from '../user/UserCredit.js';
import { UserProfile } from '../user/UserProfile.js';

// Define associations between subscription models and user models

// User associations
User.hasOne(UserSubscription, { 
  foreignKey: 'user_id', 
  as: 'subscription' 
});

User.hasMany(PaymentTransaction, { 
  foreignKey: 'user_id', 
  as: 'paymentTransactions' 
});

User.hasOne(CreditReset, { 
  foreignKey: 'user_id', 
  as: 'creditReset' 
});

// UserSubscription associations
UserSubscription.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'user' 
});

UserSubscription.belongsTo(SubscriptionPlan, { 
  foreignKey: 'plan_id', 
  as: 'plan' 
});

UserSubscription.hasMany(PaymentTransaction, { 
  foreignKey: 'subscription_id', 
  as: 'transactions' 
});

// SubscriptionPlan associations
SubscriptionPlan.hasMany(UserSubscription, { 
  foreignKey: 'plan_id', 
  as: 'subscriptions' 
});

SubscriptionPlan.hasMany(PaymentTransaction, { 
  foreignKey: 'plan_id', 
  as: 'transactions' 
});

// PaymentTransaction associations
PaymentTransaction.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'user' 
});

PaymentTransaction.belongsTo(UserSubscription, { 
  foreignKey: 'subscription_id', 
  as: 'subscription' 
});

PaymentTransaction.belongsTo(SubscriptionPlan, { 
  foreignKey: 'plan_id', 
  as: 'plan' 
});

// CreditReset associations
CreditReset.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'user' 
});

export {
  SubscriptionPlan,
  UserSubscription,
  PaymentTransaction,
  CreditReset,
};
