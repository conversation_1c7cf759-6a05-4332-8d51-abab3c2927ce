import { SubscriptionPlan, UserSubscription, PaymentTransaction, CreditReset } from '../models/subscription/index.js';
import { User, UserCredit, UserProfile } from '../models/user/index.js';
import { RazorpayService } from './RazorpayService.js';
import { CreditService } from './CreditService.js';
import { SUBSCRIPTION_SYSTEM, PAYMENT_SYSTEM } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * SubscriptionService
 * Manages subscription lifecycle, plan changes, and billing
 */
export class SubscriptionService {
  /**
   * Initialize default subscription plans
   * @returns {Promise<void>}
   */
  static async initializeDefaultPlans() {
    try {
      const plans = Object.values(SUBSCRIPTION_SYSTEM.PLANS);
      
      for (const planConfig of plans) {
        const existingPlan = await SubscriptionPlan.findByType(planConfig.type);
        
        if (!existingPlan) {
          await SubscriptionPlan.createPlan({
            planType: planConfig.type,
            name: planConfig.name,
            description: `${planConfig.name} - ${planConfig.credits === -1 ? 'Unlimited' : planConfig.credits} credits`,
            price: planConfig.price,
            currency: 'INR',
            billingCycle: planConfig.billingCycle,
            credits: planConfig.credits === -1 ? 0 : planConfig.credits,
            isUnlimitedCredits: planConfig.credits === -1,
            features: JSON.stringify({
              models: planConfig.limits.models,
              support: planConfig.type === 'PRO' ? 'priority' : 'standard',
            }),
            limits: JSON.stringify(planConfig.limits),
            sortOrder: planConfig.type === 'EXPLORER' ? 1 : planConfig.type === 'CREATOR' ? 2 : planConfig.type === 'PRO' ? 3 : 4,
          });
          
          logger.info(`Created default plan: ${planConfig.name}`);
        }
      }
    } catch (error) {
      logger.error('Error initializing default plans:', error);
      throw error;
    }
  }

  /**
   * Get available subscription plans
   * @returns {Promise<Array>} Available plans
   */
  static async getAvailablePlans() {
    try {
      return await SubscriptionPlan.getActivePlans();
    } catch (error) {
      logger.error('Error fetching available plans:', error);
      throw error;
    }
  }

  /**
   * Get user's current subscription
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} Current subscription with plan details
   */
  static async getCurrentSubscription(userId) {
    try {
      const subscription = await UserSubscription.findActiveByUserId(userId);
      
      if (!subscription) {
        // Return default free plan subscription
        const freePlan = await SubscriptionPlan.findByType('EXPLORER');
        return {
          subscription: null,
          plan: freePlan,
          isActive: true,
          isFree: true,
        };
      }

      const plan = await SubscriptionPlan.findByPk(subscription.planId);
      
      return {
        subscription,
        plan,
        isActive: subscription.isActive(),
        isFree: plan?.isFree() || false,
        daysUntilBilling: subscription.getDaysUntilBilling(),
        isInGracePeriod: subscription.isInGracePeriod(),
      };
    } catch (error) {
      logger.error('Error fetching current subscription:', error);
      throw error;
    }
  }

  /**
   * Create a new subscription
   * @param {string} userId - User ID
   * @param {string} planType - Plan type
   * @returns {Promise<Object>} Created subscription and payment order
   */
  static async createSubscription(userId, planType) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const plan = await SubscriptionPlan.findByType(planType);
      if (!plan) {
        throw new Error('Plan not found');
      }

      if (plan.isFree()) {
        throw new Error('Cannot create subscription for free plan');
      }

      // Check if user already has an active subscription
      const existingSubscription = await UserSubscription.findActiveByUserId(userId);
      if (existingSubscription) {
        throw new Error('User already has an active subscription');
      }

      const razorpayService = RazorpayService.getInstance();
      
      // Create Razorpay customer if not exists
      const userProfile = await UserProfile.findByUserId(userId);
      const customerData = {
        name: userProfile?.getFullName() || 'User',
        email: user.email,
        contact: user.mobile,
        notes: {
          userId: userId,
          planType: planType,
        },
      };

      const customer = await razorpayService.createCustomer(customerData);

      // Create payment order for subscription
      const orderData = {
        amount: RazorpayService.convertToPaise(plan.price),
        currency: plan.currency,
        receipt: `sub_${userId}_${Date.now()}`,
        notes: {
          userId: userId,
          planId: plan.id,
          planType: planType,
          type: 'subscription',
        },
      };

      const order = await razorpayService.createOrder(orderData);

      // Create payment transaction record
      const transaction = await PaymentTransaction.createTransaction({
        userId: userId,
        planId: plan.id,
        transactionType: PAYMENT_SYSTEM.TRANSACTION_TYPES.SUBSCRIPTION,
        amount: plan.price,
        currency: plan.currency,
        status: PAYMENT_SYSTEM.STATUS.PENDING,
        razorpayOrderId: order.id,
      });

      return {
        order,
        transaction,
        plan,
        customer,
      };
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Activate subscription after successful payment
   * @param {string} razorpayPaymentId - Razorpay payment ID
   * @param {string} razorpayOrderId - Razorpay order ID
   * @param {string} razorpaySignature - Razorpay signature
   * @returns {Promise<Object>} Activated subscription
   */
  static async activateSubscription(razorpayPaymentId, razorpayOrderId, razorpaySignature) {
    try {
      // Verify payment signature
      const razorpayService = RazorpayService.getInstance();
      const isValidSignature = razorpayService.verifyPaymentSignature({
        razorpay_payment_id: razorpayPaymentId,
        razorpay_order_id: razorpayOrderId,
        razorpay_signature: razorpaySignature,
      });

      if (!isValidSignature) {
        throw new Error('Invalid payment signature');
      }

      // Find transaction by order ID
      const transaction = await PaymentTransaction.findByRazorpayOrderId(razorpayOrderId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Mark transaction as successful
      await transaction.markAsSuccessful(razorpayPaymentId);

      // Get plan details
      const plan = await SubscriptionPlan.findByPk(transaction.planId);
      if (!plan) {
        throw new Error('Plan not found');
      }

      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      const nextBillingDate = new Date();

      if (plan.billingCycle === 'MONTHLY') {
        endDate.setMonth(endDate.getMonth() + 1);
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
      } else if (plan.billingCycle === 'YEARLY') {
        endDate.setFullYear(endDate.getFullYear() + 1);
        nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
      }

      // Create subscription record
      const subscription = await UserSubscription.createSubscription({
        userId: transaction.userId,
        planId: plan.id,
        status: 'ACTIVE',
        startDate: startDate,
        endDate: endDate,
        nextBillingDate: nextBillingDate,
      });

      // Update transaction with subscription ID
      transaction.subscriptionId = subscription.id;
      await transaction.save();

      // Update user profile plan
      const userProfile = await UserProfile.findByUserId(transaction.userId);
      if (userProfile) {
        userProfile.plan = plan.planType;
        await userProfile.save();
      }

      // Set up credit system for the new plan
      await this.setupUserCredits(transaction.userId, plan);

      logger.info(`Subscription activated for user ${transaction.userId}, plan ${plan.planType}`);

      return {
        subscription,
        plan,
        transaction,
      };
    } catch (error) {
      logger.error('Error activating subscription:', error);
      throw error;
    }
  }

  /**
   * Set up user credits based on plan
   * @param {string} userId - User ID
   * @param {Object} plan - Subscription plan
   * @returns {Promise<void>}
   */
  static async setupUserCredits(userId, plan) {
    try {
      // Update user credits
      if (plan.isUnlimitedCredits) {
        // For unlimited plans, set a high credit amount
        await CreditService.setUserCredits(userId, 999999, 'Unlimited plan activated');
      } else {
        await CreditService.setUserCredits(userId, plan.credits, `${plan.name} activated`);
      }

      // Set up credit reset cycle
      if (plan.billingCycle === 'WEEKLY') {
        await CreditReset.findOrCreateByUserId(userId, 'WEEKLY', plan.credits);
      } else if (plan.billingCycle === 'MONTHLY') {
        await CreditReset.findOrCreateByUserId(userId, 'MONTHLY', plan.credits);
      }

      logger.info(`Credits set up for user ${userId}, plan ${plan.planType}`);
    } catch (error) {
      logger.error('Error setting up user credits:', error);
      throw error;
    }
  }

  /**
   * Cancel user subscription
   * @param {string} userId - User ID
   * @param {string} reason - Cancellation reason
   * @returns {Promise<Object>} Cancelled subscription
   */
  static async cancelSubscription(userId, reason = 'User requested') {
    try {
      const subscription = await UserSubscription.findActiveByUserId(userId);
      if (!subscription) {
        throw new Error('No active subscription found');
      }

      // Cancel subscription
      await subscription.cancel(reason);

      // Downgrade to free plan
      await this.downgradeToFreePlan(userId);

      logger.info(`Subscription cancelled for user ${userId}`);

      return subscription;
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Downgrade user to free plan
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async downgradeToFreePlan(userId) {
    try {
      const freePlan = await SubscriptionPlan.findByType('EXPLORER');
      if (!freePlan) {
        throw new Error('Free plan not found');
      }

      // Update user profile
      const userProfile = await UserProfile.findByUserId(userId);
      if (userProfile) {
        userProfile.plan = 'EXPLORER';
        await userProfile.save();
      }

      // Reset credits to free plan limits
      await CreditService.setUserCredits(userId, freePlan.credits, 'Downgraded to free plan');

      // Set up weekly credit reset for free plan
      await CreditReset.findOrCreateByUserId(userId, 'WEEKLY', freePlan.credits);

      logger.info(`User ${userId} downgraded to free plan`);
    } catch (error) {
      logger.error('Error downgrading to free plan:', error);
      throw error;
    }
  }
}
