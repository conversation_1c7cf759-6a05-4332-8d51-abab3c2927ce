# Subscription System Documentation

## Overview

The Infini AI backend now includes a comprehensive subscription management system with Razorpay integration for secure payment processing. This system supports multiple subscription tiers, automatic credit management, and fallback mechanisms.

## Features

- **Multiple Subscription Plans**: Explorer (Free), Creator, Pro, and Add-on packs
- **Razorpay Integration**: Secure payment processing with webhooks
- **Automatic Credit Management**: Weekly/monthly credit resets based on plan
- **Fallback System**: Automatic downgrade to free tier on payment failure
- **Grace Period**: 3-day grace period for failed payments
- **Payment History**: Complete transaction tracking and history
- **Webhook Support**: Real-time payment and subscription status updates

## Subscription Plans

### 🔹 Explorer Plan (Free)
- **Price**: ₹0/month
- **Credits**: 30 credits/week (resets every 7 days)
- **Projects**: 3 (fixed)
- **File Uploads**: 3 files/day
- **Models**: Basic (GPT-3.5, <PERSON>)

### 🔸 Creator Plan (Mid Tier)
- **Price**: ₹1299/month
- **Credits**: 1500 credits/month (resets every 30 days)
- **Projects**: 20 projects
- **File Uploads**: 20 files/day
- **Models**: All models including GPT-4, <PERSON> Opus

### 🔺 Pro Plan (Top Tier)
- **Price**: ₹1899/month
- **Credits**: Unlimited (Fair Usage Policy)
- **Projects**: 100 projects
- **File Uploads**: 50 files/day
- **Models**: All models + priority access

### 🔧 Infini Add Pack
- **Price**: ₹120 (one-time)
- **Credits**: 100 (no expiry)
- **Projects**: +2 additional projects
- **File Uploads**: +10 additional files

## API Endpoints

### Subscription Management

#### Get Available Plans
```http
GET /api/subscription/plans
```

#### Get Current Subscription
```http
GET /api/subscription/current
Authorization: Bearer <token>
```

#### Create Subscription
```http
POST /api/subscription/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "planType": "CREATOR"
}
```

#### Cancel Subscription
```http
POST /api/subscription/cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "User requested cancellation"
}
```

#### Create Addon Order
```http
POST /api/subscription/addon
Authorization: Bearer <token>
```

#### Get Subscription Stats
```http
GET /api/subscription/stats
Authorization: Bearer <token>
```

### Payment Management

#### Verify Payment
```http
POST /api/payment/verify
Content-Type: application/json

{
  "razorpay_payment_id": "pay_xxxxx",
  "razorpay_order_id": "order_xxxxx",
  "razorpay_signature": "signature_xxxxx"
}
```

#### Webhook Handler
```http
POST /api/payment/webhook
X-Razorpay-Signature: <webhook_signature>
```

#### Get Payment History
```http
GET /api/payment/history?limit=10&offset=0
Authorization: Bearer <token>
```

#### Get Payment Stats
```http
GET /api/payment/stats
Authorization: Bearer <token>
```

#### Get Transaction Details
```http
GET /api/payment/transaction/:transactionId
Authorization: Bearer <token>
```

## Database Schema

### subscription_plans
- Stores plan definitions and pricing
- Includes features and limits as JSON
- Razorpay plan ID for recurring payments

### user_subscriptions
- User's active subscription information
- Status tracking (ACTIVE, SUSPENDED, CANCELLED, EXPIRED)
- Billing dates and grace periods

### payment_transactions
- Complete payment history
- Razorpay transaction details
- Status tracking and failure reasons

### credit_resets
- Credit reset cycle management
- Automatic reset scheduling
- Reset history tracking

## Environment Variables

Add these to your `.env` file:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret_here
```

## Setup Instructions

### 1. Install Dependencies
```bash
npm install razorpay
```

### 2. Run Database Migration
```bash
node scripts/run-subscription-migration.js
```

### 3. Configure Razorpay
1. Create a Razorpay account
2. Get API keys from dashboard
3. Set up webhook endpoint: `https://yourdomain.com/api/payment/webhook`
4. Add webhook secret to environment variables

### 4. Start the Server
```bash
npm run dev
```

## Webhook Events

The system handles these Razorpay webhook events:

- `payment.authorized` - Payment authorized
- `payment.captured` - Payment captured successfully
- `payment.failed` - Payment failed
- `subscription.activated` - Subscription activated
- `subscription.charged` - Recurring payment charged
- `subscription.cancelled` - Subscription cancelled
- `subscription.completed` - Subscription completed
- `subscription.halted` - Payment failed, subscription halted
- `invoice.paid` - Invoice paid
- `invoice.payment_failed` - Invoice payment failed

## Credit Reset System

### Weekly Reset (Explorer Plan)
- Resets every 7 days from subscription start
- Credits reset to 30

### Monthly Reset (Paid Plans)
- Resets every 30 days from subscription start
- Credits reset to plan amount (1500 for Creator, unlimited for Pro)

### Automatic Processing
- Background job checks for due resets
- Automatic credit replenishment
- Reset history tracking

## Fallback Mechanism

### Payment Failure Handling
1. **Payment Fails** → Subscription status set to SUSPENDED
2. **Grace Period** → 3 days to retry payment
3. **Grace Expired** → Automatic downgrade to Explorer plan
4. **Credit Reset** → Credits reset to free tier limits

### Subscription Cancellation
1. **User Cancels** → Subscription marked as CANCELLED
2. **Immediate Downgrade** → Switch to Explorer plan
3. **Credit Adjustment** → Reset to free tier credits
4. **Feature Restriction** → Limit access to basic models

## Security Features

- **Payment Signature Verification** - All payments verified with HMAC
- **Webhook Signature Verification** - Webhooks verified with secret
- **Transaction Logging** - Complete audit trail
- **Secure API Keys** - Environment variable storage
- **User Authorization** - JWT-based authentication

## Testing

### Test Cards (Razorpay Test Mode)
- **Success**: 4111 1111 1111 1111
- **Failure**: 4000 0000 0000 0002
- **CVV**: Any 3 digits
- **Expiry**: Any future date

### Test Workflow
1. Create subscription order
2. Use test card for payment
3. Verify webhook events
4. Check subscription activation
5. Test credit reset functionality

## Monitoring

### Key Metrics to Monitor
- Subscription conversion rates
- Payment success rates
- Churn rates
- Revenue metrics
- Failed payment recovery

### Logging
- All payment transactions logged
- Webhook events logged
- Subscription changes logged
- Error tracking and alerting

## Support

For issues or questions:
1. Check logs for error details
2. Verify Razorpay configuration
3. Test webhook connectivity
4. Review database constraints
5. Contact development team

## Future Enhancements

- **Proration**: Handle mid-cycle plan changes
- **Coupons**: Discount code support
- **Analytics**: Advanced subscription analytics
- **Notifications**: Email notifications for subscription events
- **Multi-currency**: Support for multiple currencies
